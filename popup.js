// Chrome扩展 AES加解密工具 - 弹窗脚本

document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabPanes = document.querySelectorAll('.tab-pane');
    const encryptBtn = document.getElementById('encrypt-btn');
    const decryptBtn = document.getElementById('decrypt-btn');
    const clearEncryptBtn = document.getElementById('clear-encrypt-btn');
    const clearDecryptBtn = document.getElementById('clear-decrypt-btn');
    const copyEncryptBtn = document.getElementById('copy-encrypt-btn');
    const copyDecryptBtn = document.getElementById('copy-decrypt-btn');
    const clearHistoryBtn = document.getElementById('clear-history-btn');
    const statusMessage = document.getElementById('status-message');
    const historyList = document.getElementById('history-list');

    // 标签页切换功能
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            
            // 移除所有活动状态
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));
            
            // 添加当前活动状态
            this.classList.add('active');
            document.getElementById(targetTab + '-tab').classList.add('active');
        });
    });

    // 显示状态消息
    function showStatus(message, type = 'success') {
        statusMessage.textContent = message;
        statusMessage.className = `status-message ${type}`;
        statusMessage.style.display = 'block';
        
        setTimeout(() => {
            statusMessage.style.display = 'none';
        }, 3000);
    }

    // 加密功能
    encryptBtn.addEventListener('click', function() {
        const inputText = document.getElementById('encrypt-input').value.trim();
        const outputElement = document.getElementById('encrypt-output');
        const algorithm = document.getElementById('encrypt-algorithm').value;

        if (!inputText) {
            showStatus('请输入要加密的文本', 'error');
            return;
        }

        try {
            // 检查AESUtil是否可用
            if (typeof window.AESUtil === 'undefined') {
                showStatus('加密库未加载，请刷新页面重试', 'error');
                return;
            }

            let encryptedText;
            let algorithmName;

            switch (algorithm) {
                case 'AES':
                    encryptedText = window.AESUtil.encryptAES(inputText);
                    algorithmName = 'AES';
                    break;
                case 'RSA':
                    encryptedText = window.AESUtil.encryptRSA(inputText, window.AESUtil.appConfig?.PublicKey);
                    algorithmName = 'RSA(私钥)';
                    break;
                case 'RSAByPublic':
                    encryptedText = window.AESUtil.encryptRSAByPublic(inputText, window.AESUtil.appConfig?.PublicKey);
                    algorithmName = 'RSA(公钥)';
                    break;
                case 'DES':
                    encryptedText = window.AESUtil.encryptDES(inputText);
                    algorithmName = 'DES';
                    break;
                default:
                    throw new Error('不支持的加密算法');
            }

            outputElement.value = encryptedText;

            // 添加到历史记录
            addToHistory(`${algorithmName}加密`, inputText.substring(0, 30) + (inputText.length > 30 ? '...' : ''));

            showStatus(`${algorithmName}加密成功！`);
        } catch (error) {
            console.error('加密失败:', error);
            showStatus('加密失败，请检查输入内容和算法选择', 'error');
        }
    });

    // 解密功能
    decryptBtn.addEventListener('click', function() {
        const inputText = document.getElementById('decrypt-input').value.trim();
        const outputElement = document.getElementById('decrypt-output');
        const algorithm = document.getElementById('decrypt-algorithm').value;

        if (!inputText) {
            showStatus('请输入要解密的文本', 'error');
            return;
        }

        try {
            // 检查AESUtil是否可用
            if (typeof window.AESUtil === 'undefined') {
                showStatus('解密库未加载，请刷新页面重试', 'error');
                return;
            }

            let decryptedText;
            let algorithmName;

            switch (algorithm) {
                case 'AES':
                    decryptedText = window.AESUtil.decryptAES(inputText);
                    algorithmName = 'AES';
                    break;
                case 'RSA':
                    decryptedText = window.AESUtil.decryptRSA(inputText, window.AESUtil.appConfig?.PublicKey);
                    algorithmName = 'RSA(公钥)';
                    break;
                case 'RSAByPrivate':
                    decryptedText = window.AESUtil.decryptRSAByPrivate(inputText, window.AESUtil.appConfig?.PublicKey);
                    algorithmName = 'RSA(私钥)';
                    break;
                case 'DES':
                    decryptedText = window.AESUtil.decryptDES(inputText);
                    algorithmName = 'DES';
                    break;
                default:
                    throw new Error('不支持的解密算法');
            }

            outputElement.value = decryptedText;

            // 添加到历史记录
            addToHistory(`${algorithmName}解密`, '密文解密操作');

            showStatus(`${algorithmName}解密成功！`);
        } catch (error) {
            console.error('解密失败:', error);
            showStatus('解密失败，请检查密文格式和算法选择是否正确', 'error');
        }
    });

    // 清空输入功能
    clearEncryptBtn.addEventListener('click', function() {
        document.getElementById('encrypt-input').value = '';
        document.getElementById('encrypt-output').value = '';
        showStatus('已清空加密内容');
    });

    clearDecryptBtn.addEventListener('click', function() {
        document.getElementById('decrypt-input').value = '';
        document.getElementById('decrypt-output').value = '';
        showStatus('已清空解密内容');
    });

    // 复制结果功能
    copyEncryptBtn.addEventListener('click', function() {
        const outputText = document.getElementById('encrypt-output').value;
        if (!outputText) {
            showStatus('没有可复制的内容', 'error');
            return;
        }
        copyToClipboard(outputText, '加密结果已复制到剪贴板');
    });

    copyDecryptBtn.addEventListener('click', function() {
        const outputText = document.getElementById('decrypt-output').value;
        if (!outputText) {
            showStatus('没有可复制的内容', 'error');
            return;
        }
        copyToClipboard(outputText, '解密结果已复制到剪贴板');
    });

    // 复制到剪贴板
    function copyToClipboard(text, successMessage) {
        navigator.clipboard.writeText(text).then(function() {
            showStatus(successMessage);
        }).catch(function(err) {
            console.error('复制失败:', err);
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                showStatus(successMessage);
            } catch (fallbackErr) {
                showStatus('复制失败，请手动复制', 'error');
            }
            document.body.removeChild(textArea);
        });
    }

    // 添加到历史记录
    function addToHistory(operation, content) {
        const now = new Date();
        const timeString = now.toLocaleTimeString('zh-CN', { 
            hour12: false, 
            hour: '2-digit', 
            minute: '2-digit' 
        });
        
        const historyItem = {
            time: timeString,
            operation: operation,
            content: content,
            timestamp: now.getTime()
        };

        // 获取现有历史记录
        chrome.storage.local.get(['encryptHistory'], function(result) {
            let history = result.encryptHistory || [];
            
            // 添加新记录到开头
            history.unshift(historyItem);
            
            // 限制历史记录数量（最多保留20条）
            if (history.length > 20) {
                history = history.slice(0, 20);
            }
            
            // 保存到存储
            chrome.storage.local.set({ encryptHistory: history }, function() {
                displayHistory();
            });
        });
    }

    // 显示历史记录
    function displayHistory() {
        chrome.storage.local.get(['encryptHistory'], function(result) {
            const history = result.encryptHistory || [];
            
            if (history.length === 0) {
                historyList.innerHTML = '<div class="no-history">暂无操作历史</div>';
                return;
            }
            
            historyList.innerHTML = history.map(item => `
                <div class="history-item" data-content="${item.content}">
                    <div class="operation">${item.operation}: ${item.content}</div>
                    <div class="time">${item.time}</div>
                </div>
            `).join('');
            
            // 为历史记录项添加点击事件
            document.querySelectorAll('.history-item').forEach(item => {
                item.addEventListener('click', function() {
                    const content = this.getAttribute('data-content');
                    const operation = this.querySelector('.operation').textContent;
                    
                    if (operation.startsWith('加密')) {
                        document.getElementById('encrypt-input').value = content.replace('...', '');
                        // 切换到加密标签页
                        document.querySelector('[data-tab="encrypt"]').click();
                    }
                });
            });
        });
    }

    // 清空历史记录
    clearHistoryBtn.addEventListener('click', function() {
        chrome.storage.local.set({ encryptHistory: [] }, function() {
            displayHistory();
            showStatus('历史记录已清空');
        });
    });

    // 页面加载时显示历史记录
    displayHistory();

    // 键盘快捷键支持
    document.addEventListener('keydown', function(e) {
        // Ctrl+Enter 执行当前标签页的操作
        if (e.ctrlKey && e.key === 'Enter') {
            const activeTab = document.querySelector('.tab-pane.active');
            if (activeTab.id === 'encrypt-tab') {
                encryptBtn.click();
            } else {
                decryptBtn.click();
            }
        }
        
        // Ctrl+L 清空当前标签页
        if (e.ctrlKey && e.key === 'l') {
            e.preventDefault();
            const activeTab = document.querySelector('.tab-pane.active');
            if (activeTab.id === 'encrypt-tab') {
                clearEncryptBtn.click();
            } else {
                clearDecryptBtn.click();
            }
        }
    });
});
