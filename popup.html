<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AES加解密工具</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 AES加解密工具</h1>
        </div>
        
        <div class="main-content">
            <div class="tab-container">
                <div class="tab-buttons">
                    <button class="tab-btn active" data-tab="encrypt">加密</button>
                    <button class="tab-btn" data-tab="decrypt">解密</button>
                </div>
                
                <div class="tab-content">
                    <!-- 加密标签页 -->
                    <div id="encrypt-tab" class="tab-pane active">
                        <div class="input-group">
                            <label for="encrypt-input">请输入要加密的文本：</label>
                            <textarea id="encrypt-input" placeholder="在此输入需要加密的文本内容..." rows="4"></textarea>
                        </div>
                        
                        <div class="button-group">
                            <button id="encrypt-btn" class="action-btn encrypt-btn">🔒 加密</button>
                            <button id="clear-encrypt-btn" class="clear-btn">清空</button>
                        </div>
                        
                        <div class="output-group">
                            <label for="encrypt-output">加密结果：</label>
                            <textarea id="encrypt-output" placeholder="加密结果将显示在这里..." rows="4" readonly></textarea>
                            <button id="copy-encrypt-btn" class="copy-btn">📋 复制结果</button>
                        </div>
                    </div>
                    
                    <!-- 解密标签页 -->
                    <div id="decrypt-tab" class="tab-pane">
                        <div class="input-group">
                            <label for="decrypt-input">请输入要解密的文本：</label>
                            <textarea id="decrypt-input" placeholder="在此输入需要解密的密文内容..." rows="4"></textarea>
                        </div>
                        
                        <div class="button-group">
                            <button id="decrypt-btn" class="action-btn decrypt-btn">🔓 解密</button>
                            <button id="clear-decrypt-btn" class="clear-btn">清空</button>
                        </div>
                        
                        <div class="output-group">
                            <label for="decrypt-output">解密结果：</label>
                            <textarea id="decrypt-output" placeholder="解密结果将显示在这里..." rows="4" readonly></textarea>
                            <button id="copy-decrypt-btn" class="copy-btn">📋 复制结果</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 状态提示 -->
            <div id="status-message" class="status-message"></div>
            
            <!-- 历史记录 -->
            <div class="history-section">
                <div class="history-header">
                    <h3>📝 操作历史</h3>
                    <button id="clear-history-btn" class="clear-history-btn">清空历史</button>
                </div>
                <div id="history-list" class="history-list">
                    <div class="no-history">暂无操作历史</div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <div class="version-info">版本 1.0.0</div>
        </div>
    </div>
    
    <!-- 引入依赖库 -->
    <script src="libs/crypto-js.min.js"></script>
    <script src="libs/jsencrypt.min.js"></script>
    <script src="libs/aesUtil.js"></script>
    <script src="popup.js"></script>
</body>
</html>
