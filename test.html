<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AES加解密工具测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .test-section h2 {
            color: #495057;
            margin-bottom: 15px;
        }
        
        .test-text {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            line-height: 1.6;
        }
        
        .selectable {
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        
        .selectable:hover {
            background: #e3f2fd;
        }
        
        .selectable::selection {
            background: #4facfe;
            color: white;
        }
        
        .instructions {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .instructions h3 {
            margin-top: 0;
        }
        
        .shortcut {
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 8px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-weight: bold;
        }
        
        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 AES加解密工具测试页面</h1>
        
        <div class="instructions">
            <h3>使用说明</h3>
            <p>1. 确保已安装并启用AES加解密工具扩展</p>
            <p>2. 选中下方的测试文本</p>
            <p>3. 使用快捷键进行加解密操作：</p>
            <ul>
                <li><span class="shortcut">Ctrl+Shift+E</span> - 加密选中文本</li>
                <li><span class="shortcut">Ctrl+Shift+D</span> - 解密选中文本</li>
            </ul>
            <p>4. 或者点击扩展图标使用界面进行操作</p>
            <p>5. 支持多种加解密算法：<strong>AES</strong>、<strong>RSA</strong>、<strong>DES</strong></p>
        </div>
        
        <div class="test-section">
            <h2>📝 测试文本 - 普通文本</h2>
            <div class="test-text selectable">
                这是一段用于测试加密功能的中文文本。请选中这段文字，然后使用 Ctrl+Shift+E 进行加密测试。
            </div>
            <div class="test-text selectable">
                Hello World! This is a test message for encryption. Please select this text and use Ctrl+Shift+E to encrypt.
            </div>
        </div>
        
        <div class="test-section">
            <h2>🔒 测试文本 - JSON数据</h2>
            <div class="test-text selectable">
                {"name": "张三", "age": 25, "email": "<EMAIL>", "address": "北京市朝阳区"}
            </div>
            <div class="test-text selectable">
                {"status": "success", "data": {"id": 12345, "token": "abc123def456", "expires": "2024-12-31"}}
            </div>
        </div>
        
        <div class="test-section">
            <h2>🔓 测试文本 - 已加密数据</h2>
            <p>以下是一些示例加密数据，可以用于测试解密功能：</p>
            <div class="test-text selectable">
                U2FsdGVkX1+vupppZksvRf5pq5g5XjFRIipRkwB0K1Y=
            </div>
            <div class="test-text selectable">
                U2FsdGVkX19ZM6uygiBJp1+vupppZksvRf5pq5g5XjFRIipRkwB0K1Y96L+uw==
            </div>
        </div>
        
        <div class="test-section">
            <h2>📊 测试文本 - 长文本</h2>
            <div class="test-text selectable">
                这是一段较长的测试文本，用于验证加密工具对长文本的处理能力。在实际应用中，我们经常需要加密各种长度的文本内容，包括用户输入的表单数据、API请求参数、配置信息等。这个测试文本包含了中文字符、标点符号和数字123456，可以全面测试加密算法的兼容性和稳定性。请选中整段文字进行加密测试，验证扩展是否能够正确处理长文本内容。
            </div>
        </div>
        
        <div class="test-section">
            <h2>🌐 测试文本 - 特殊字符</h2>
            <div class="test-text selectable">
                !@#$%^&*()_+-=[]{}|;':\",./<>?`~
            </div>
            <div class="test-text selectable">
                测试特殊字符：©®™€£¥§¶•‰‱°′″‴⁄
            </div>
            <div class="test-text selectable">
                Emoji测试：😀😃😄😁😆😅😂🤣😊😇🙂🙃😉😌😍🥰😘
            </div>
        </div>
        
        <div class="note">
            <strong>注意：</strong>
            <ul>
                <li>如果快捷键不工作，请检查扩展是否正确安装并启用</li>
                <li>加密后的结果会显示在页面右上角的通知中</li>
                <li>可以通过点击扩展图标打开界面查看详细结果</li>
                <li>所有操作都会自动保存到历史记录中</li>
            </ul>
        </div>
    </div>
    
    <script>
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const selectableElements = document.querySelectorAll('.selectable');
            
            selectableElements.forEach(element => {
                element.addEventListener('click', function() {
                    // 选中整个元素的文本
                    const range = document.createRange();
                    range.selectNodeContents(this);
                    const selection = window.getSelection();
                    selection.removeAllRanges();
                    selection.addRange(range);
                });
            });
            
            // 监听选择事件
            document.addEventListener('selectionchange', function() {
                const selection = window.getSelection();
                const selectedText = selection.toString().trim();
                
                if (selectedText.length > 0) {
                    console.log('选中文本:', selectedText.substring(0, 50) + '...');
                }
            });
        });
    </script>
</body>
</html>
