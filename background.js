// Chrome扩展 AES加解密工具 - 背景脚本

// 扩展安装时的初始化
chrome.runtime.onInstalled.addListener(function(details) {
    console.log('AES加解密工具扩展已安装');
    
    if (details.reason === 'install') {
        // 首次安装时的处理
        console.log('首次安装AES加解密工具');
        
        // 初始化存储
        chrome.storage.local.set({
            encryptHistory: [],
            settings: {
                autoSave: true,
                maxHistoryItems: 20
            }
        });
        
        // 可以在这里打开欢迎页面或设置页面
        // chrome.tabs.create({ url: 'welcome.html' });
    } else if (details.reason === 'update') {
        console.log('AES加解密工具已更新到版本:', chrome.runtime.getManifest().version);
    }
});

// 处理来自popup或content script的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    console.log('收到消息:', request);
    
    switch (request.action) {
        case 'encrypt':
            handleEncryption(request.data, sendResponse);
            break;
            
        case 'decrypt':
            handleDecryption(request.data, sendResponse);
            break;
            
        case 'getHistory':
            getEncryptionHistory(sendResponse);
            break;
            
        case 'clearHistory':
            clearEncryptionHistory(sendResponse);
            break;
            
        case 'saveToHistory':
            saveToHistory(request.data, sendResponse);
            break;
            
        default:
            console.log('未知的消息类型:', request.action);
            sendResponse({ success: false, error: '未知的操作类型' });
    }
    
    // 返回true表示异步响应
    return true;
});

// 处理加密请求
function handleEncryption(data, sendResponse) {
    try {
        // 这里可以添加额外的验证或处理逻辑
        console.log('处理加密请求:', data.text.substring(0, 50) + '...');
        
        // 由于加密逻辑在popup中处理，这里主要用于日志记录
        sendResponse({ 
            success: true, 
            message: '加密请求已处理' 
        });
        
    } catch (error) {
        console.error('加密处理失败:', error);
        sendResponse({ 
            success: false, 
            error: error.message 
        });
    }
}

// 处理解密请求
function handleDecryption(data, sendResponse) {
    try {
        console.log('处理解密请求');
        
        // 由于解密逻辑在popup中处理，这里主要用于日志记录
        sendResponse({ 
            success: true, 
            message: '解密请求已处理' 
        });
        
    } catch (error) {
        console.error('解密处理失败:', error);
        sendResponse({ 
            success: false, 
            error: error.message 
        });
    }
}

// 获取加密历史记录
function getEncryptionHistory(sendResponse) {
    chrome.storage.local.get(['encryptHistory'], function(result) {
        const history = result.encryptHistory || [];
        sendResponse({ 
            success: true, 
            data: history 
        });
    });
}

// 清空加密历史记录
function clearEncryptionHistory(sendResponse) {
    chrome.storage.local.set({ encryptHistory: [] }, function() {
        console.log('历史记录已清空');
        sendResponse({ 
            success: true, 
            message: '历史记录已清空' 
        });
    });
}

// 保存到历史记录
function saveToHistory(data, sendResponse) {
    chrome.storage.local.get(['encryptHistory'], function(result) {
        let history = result.encryptHistory || [];
        
        const historyItem = {
            time: new Date().toLocaleTimeString('zh-CN'),
            operation: data.operation,
            content: data.content,
            timestamp: Date.now()
        };
        
        // 添加到历史记录开头
        history.unshift(historyItem);
        
        // 限制历史记录数量
        if (history.length > 20) {
            history = history.slice(0, 20);
        }
        
        chrome.storage.local.set({ encryptHistory: history }, function() {
            console.log('已保存到历史记录:', historyItem);
            sendResponse({ 
                success: true, 
                message: '已保存到历史记录' 
            });
        });
    });
}

// 监听扩展图标点击事件（可选）
chrome.action.onClicked.addListener(function(tab) {
    console.log('扩展图标被点击');
    // 由于我们使用popup，这个事件通常不会触发
    // 但可以在这里添加备用逻辑
});

// 监听存储变化
chrome.storage.onChanged.addListener(function(changes, namespace) {
    console.log('存储发生变化:', changes, namespace);
    
    if (changes.encryptHistory) {
        console.log('历史记录已更新，新记录数量:', 
            changes.encryptHistory.newValue ? changes.encryptHistory.newValue.length : 0);
    }
});

// 定期清理过期的历史记录（可选）
function cleanupOldHistory() {
    chrome.storage.local.get(['encryptHistory'], function(result) {
        const history = result.encryptHistory || [];
        const oneWeekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000); // 一周前
        
        const filteredHistory = history.filter(item => 
            item.timestamp && item.timestamp > oneWeekAgo
        );
        
        if (filteredHistory.length !== history.length) {
            chrome.storage.local.set({ encryptHistory: filteredHistory }, function() {
                console.log('已清理过期历史记录，剩余:', filteredHistory.length);
            });
        }
    });
}

// 每天清理一次过期记录
setInterval(cleanupOldHistory, 24 * 60 * 60 * 1000);

console.log('AES加解密工具背景脚本已加载');
