# AES加解密工具 Chrome扩展 - 安装指南

## 快速安装

### 方法一：使用自动安装脚本（推荐）

1. **运行安装脚本**
   ```powershell
   # 在项目根目录下运行
   .\setup.ps1
   ```
   
2. **安装扩展**
   - 打开 Chrome 浏览器
   - 进入 `chrome://extensions/`
   - 开启"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择项目根目录

### 方法二：手动安装

1. **下载依赖库**
   
   下载以下文件并保存到 `libs/` 目录：
   
   - **crypto-js.min.js**
     ```
     https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js
     ```
   
   - **jsencrypt.min.js**
     ```
     https://cdnjs.cloudflare.com/ajax/libs/jsencrypt/3.3.2/jsencrypt.min.js
     ```

2. **验证文件结构**
   ```
   mt-coder/
   ├── manifest.json
   ├── popup.html
   ├── popup.css
   ├── popup.js
   ├── background.js
   ├── content.js
   ├── libs/
   │   ├── aesUtil.js
   │   ├── crypto-js.min.js    ← 需要下载
   │   └── jsencrypt.min.js    ← 需要下载
   └── icons/
       ├── icon16.png
       ├── icon32.png
       ├── icon48.png
       └── icon128.png
   ```

3. **安装扩展**
   - 打开 Chrome 浏览器
   - 地址栏输入 `chrome://extensions/`
   - 右上角开启"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择项目根目录 `mt-coder`

## 验证安装

### 1. 检查扩展状态
- 在 `chrome://extensions/` 页面中应该能看到"AES加解密工具"
- 扩展状态应该是"已启用"
- 浏览器工具栏应该显示扩展图标

### 2. 测试基本功能
1. 点击扩展图标
2. 在弹出的界面中输入测试文本
3. 点击"加密"按钮
4. 检查是否能正常显示加密结果

### 3. 测试快捷键功能
1. 打开 `test.html` 文件
2. 选中页面中的测试文本
3. 使用 `Ctrl+Shift+E` 进行加密
4. 检查是否显示成功通知

## 常见问题

### Q: 扩展无法加载？
**A:** 检查以下几点：
- 确认已开启"开发者模式"
- 检查 `manifest.json` 文件是否存在且格式正确
- 查看扩展管理页面是否有错误提示

### Q: 加密功能不工作？
**A:** 可能的原因：
- 依赖库文件未正确下载
- 检查浏览器控制台是否有错误信息
- 确认 `libs/` 目录下的文件是否完整

### Q: 快捷键不响应？
**A:** 尝试以下解决方案：
- 刷新页面后重试
- 检查是否与其他扩展快捷键冲突
- 确认扩展已正确安装并启用

### Q: 依赖库下载失败？
**A:** 手动下载解决：
1. 访问上述CDN链接
2. 右键"另存为"保存文件
3. 确保文件名正确
4. 检查文件大小（crypto-js约300KB，jsencrypt约50KB）

## 卸载扩展

1. 进入 `chrome://extensions/`
2. 找到"AES加解密工具"
3. 点击"移除"按钮
4. 确认删除

## 更新扩展

1. 修改代码后保存
2. 进入 `chrome://extensions/`
3. 找到扩展，点击刷新按钮 🔄
4. 或者重新加载扩展

## 技术支持

如果遇到问题：
1. 查看浏览器控制台错误信息
2. 检查扩展管理页面的错误提示
3. 参考 `README.md` 中的详细说明
4. 通过 GitHub Issues 报告问题

## 开发模式

如需进行开发调试：

1. **启用调试模式**
   ```javascript
   // 在浏览器控制台中执行
   localStorage.setItem('aes-tool-debug', 'true');
   ```

2. **查看调试信息**
   - 打开浏览器开发者工具
   - 查看 Console 标签页的日志信息
   - 检查 Network 标签页的网络请求

3. **调试扩展脚本**
   - 在扩展管理页面点击"检查视图"
   - 可以调试 popup、background 等脚本

---

**注意：** 本扩展仅供学习和测试使用，请确保在安全的环境中使用，并妥善保管加密密钥。
