/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    line-height: 1.6;
}

.container {
    width: 450px;
    min-height: 600px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 20px;
    text-align: center;
}

.header h1 {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    padding: 20px;
}

/* 标签页样式 */
.tab-buttons {
    display: flex;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 4px;
    margin-bottom: 20px;
}

.tab-btn {
    flex: 1;
    padding: 10px 16px;
    border: none;
    background: transparent;
    color: #666;
    font-size: 14px;
    font-weight: 500;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-btn.active {
    background: #4facfe;
    color: white;
    box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
}

.tab-btn:hover:not(.active) {
    background: #e9ecef;
    color: #333;
}

/* 标签页内容 */
.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* 算法选择组样式 */
.algorithm-group {
    margin-bottom: 16px;
}

.algorithm-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.algorithm-select {
    width: 100%;
    padding: 10px 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    font-family: inherit;
    background: white;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.algorithm-select:focus {
    outline: none;
    border-color: #4facfe;
    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
}

.algorithm-select:hover {
    border-color: #4facfe;
}

/* 输入组样式 */
.input-group {
    margin-bottom: 16px;
}

.input-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    font-family: inherit;
    resize: vertical;
    transition: border-color 0.3s ease;
}

textarea:focus {
    outline: none;
    border-color: #4facfe;
    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
}

textarea::placeholder {
    color: #adb5bd;
}

/* 按钮组样式 */
.button-group {
    display: flex;
    gap: 10px;
    margin-bottom: 16px;
}

.action-btn {
    flex: 1;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.encrypt-btn {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
}

.encrypt-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(238, 90, 36, 0.3);
}

.decrypt-btn {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
    color: white;
}

.decrypt-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(68, 160, 141, 0.3);
}

.clear-btn {
    background: #6c757d;
    color: white;
    padding: 12px 16px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.clear-btn:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

/* 输出组样式 */
.output-group {
    position: relative;
    margin-bottom: 20px;
}

.copy-btn {
    position: absolute;
    top: 32px;
    right: 8px;
    background: #28a745;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.copy-btn:hover {
    background: #218838;
    transform: scale(1.05);
}

/* 状态消息 */
.status-message {
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 16px;
    font-size: 14px;
    text-align: center;
    display: none;
}

.status-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* 历史记录样式 */
.history-section {
    border-top: 1px solid #e9ecef;
    padding-top: 16px;
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.history-header h3 {
    font-size: 16px;
    color: #333;
}

.clear-history-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.clear-history-btn:hover {
    background: #c82333;
}

.history-list {
    max-height: 150px;
    overflow-y: auto;
}

.history-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 8px 12px;
    margin-bottom: 8px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.history-item:hover {
    background: #e9ecef;
    transform: translateX(4px);
}

.history-item .time {
    color: #6c757d;
    font-size: 11px;
}

.history-item .operation {
    font-weight: 500;
    color: #495057;
}

.no-history {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 20px;
}

/* 底部样式 */
.footer {
    background: #f8f9fa;
    padding: 12px 20px;
    text-align: center;
    border-top: 1px solid #e9ecef;
}

.version-info {
    font-size: 12px;
    color: #6c757d;
}

/* 滚动条样式 */
.history-list::-webkit-scrollbar {
    width: 6px;
}

.history-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.history-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.history-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
