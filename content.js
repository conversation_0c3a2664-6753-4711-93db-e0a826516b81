// Chrome扩展 AES加解密工具 - 内容脚本

(function() {
    'use strict';
    
    console.log('AES加解密工具内容脚本已加载');
    
    // 检查是否已经初始化过，避免重复加载
    if (window.aesToolInitialized) {
        return;
    }
    window.aesToolInitialized = true;
    
    // 创建右键菜单功能（通过消息传递实现）
    let selectedText = '';
    
    // 监听文本选择
    document.addEventListener('mouseup', function(e) {
        const selection = window.getSelection();
        selectedText = selection.toString().trim();
        
        if (selectedText.length > 0) {
            console.log('选中文本:', selectedText.substring(0, 50) + '...');
        }
    });
    
    // 监听键盘快捷键
    document.addEventListener('keydown', function(e) {
        // Ctrl+Shift+E: 快速加密选中文本
        if (e.ctrlKey && e.shiftKey && e.key === 'E') {
            e.preventDefault();
            if (selectedText) {
                quickEncrypt(selectedText);
            } else {
                showNotification('请先选择要加密的文本', 'warning');
            }
        }
        
        // Ctrl+Shift+D: 快速解密选中文本
        if (e.ctrlKey && e.shiftKey && e.key === 'D') {
            e.preventDefault();
            if (selectedText) {
                quickDecrypt(selectedText);
            } else {
                showNotification('请先选择要解密的文本', 'warning');
            }
        }
    });
    
    // 快速加密功能
    function quickEncrypt(text) {
        // 向background script发送加密请求
        chrome.runtime.sendMessage({
            action: 'encrypt',
            data: { text: text }
        }, function(response) {
            if (response && response.success) {
                showNotification('文本已加密，请打开扩展查看结果', 'success');
                // 可以选择自动复制到剪贴板
                copyToClipboard(text, '加密');
            } else {
                showNotification('加密失败: ' + (response ? response.error : '未知错误'), 'error');
            }
        });
    }
    
    // 快速解密功能
    function quickDecrypt(text) {
        // 向background script发送解密请求
        chrome.runtime.sendMessage({
            action: 'decrypt',
            data: { text: text }
        }, function(response) {
            if (response && response.success) {
                showNotification('文本已解密，请打开扩展查看结果', 'success');
                copyToClipboard(text, '解密');
            } else {
                showNotification('解密失败: ' + (response ? response.error : '未知错误'), 'error');
            }
        });
    }
    
    // 复制到剪贴板并保存到历史
    function copyToClipboard(text, operation) {
        navigator.clipboard.writeText(text).then(function() {
            // 保存到历史记录
            chrome.runtime.sendMessage({
                action: 'saveToHistory',
                data: {
                    operation: operation,
                    content: text.substring(0, 30) + (text.length > 30 ? '...' : '')
                }
            });
        }).catch(function(err) {
            console.error('复制失败:', err);
        });
    }
    
    // 显示通知
    function showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `aes-tool-notification aes-tool-${type}`;
        notification.textContent = message;
        
        // 设置样式
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 20px',
            borderRadius: '8px',
            color: 'white',
            fontFamily: 'Microsoft YaHei, sans-serif',
            fontSize: '14px',
            fontWeight: '500',
            zIndex: '10000',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',
            transition: 'all 0.3s ease',
            maxWidth: '300px',
            wordWrap: 'break-word'
        });
        
        // 根据类型设置背景色
        switch (type) {
            case 'success':
                notification.style.background = 'linear-gradient(135deg, #4ecdc4, #44a08d)';
                break;
            case 'error':
                notification.style.background = 'linear-gradient(135deg, #ff6b6b, #ee5a24)';
                break;
            case 'warning':
                notification.style.background = 'linear-gradient(135deg, #feca57, #ff9ff3)';
                break;
            default:
                notification.style.background = 'linear-gradient(135deg, #4facfe, #00f2fe)';
        }
        
        // 添加到页面
        document.body.appendChild(notification);
        
        // 动画效果
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
            notification.style.opacity = '1';
        }, 10);
        
        // 3秒后自动移除
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
        
        // 点击关闭
        notification.addEventListener('click', function() {
            notification.style.transform = 'translateX(100%)';
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        });
    }
    
    // 监听来自popup的消息
    chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
        if (request.action === 'getSelectedText') {
            sendResponse({ text: selectedText });
        } else if (request.action === 'showNotification') {
            showNotification(request.message, request.type);
            sendResponse({ success: true });
        }
        
        return true;
    });
    
    // 添加CSS样式到页面
    const style = document.createElement('style');
    style.textContent = `
        .aes-tool-notification {
            transform: translateX(100%);
            opacity: 0;
        }
        
        .aes-tool-notification:hover {
            transform: scale(1.02) !important;
            cursor: pointer;
        }
    `;
    document.head.appendChild(style);
    
    // 页面加载完成后的初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }
    
    function initialize() {
        console.log('AES加解密工具内容脚本初始化完成');
        
        // 可以在这里添加页面特定的初始化逻辑
        // 例如：检测特定的输入框，添加加密/解密按钮等
        
        // 显示初始化完成通知（仅在开发模式下）
        if (localStorage.getItem('aes-tool-debug') === 'true') {
            showNotification('AES加解密工具已就绪\n快捷键: Ctrl+Shift+E(加密) / Ctrl+Shift+D(解密)', 'info');
        }
    }
    
})();
