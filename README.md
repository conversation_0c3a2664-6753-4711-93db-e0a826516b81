# AES加解密工具 Chrome扩展插件

一个基于现有 aesUtil.js 加解密功能开发的 Chrome 浏览器扩展插件，提供便捷的文本加解密服务。

## 功能特性

### 🔐 核心功能
- **多种加密算法**: 支持AES、RSA、DES三种主流加密算法
- **AES加密/解密**: 基于现有的 aesUtil.js 实现的安全加解密
- **RSA加密/解密**: 支持RSA公钥加密/私钥解密和私钥加密/公钥解密两种模式
- **DES加密/解密**: 提供DES算法加解密支持
- **算法选择**: 界面提供下拉菜单选择不同的加解密算法

### 🎨 用户界面
- **友好的用户界面**: 简洁美观的弹窗界面
- **标签页设计**: 加密和解密功能分离，操作清晰
- **实时状态反馈**: 操作成功/失败状态提示
- **一键复制**: 快速复制加解密结果

### 📝 便捷功能
- **操作历史**: 自动保存最近20条操作记录
- **快捷键支持**: 
  - `Ctrl+Enter`: 执行当前操作
  - `Ctrl+L`: 清空当前内容
  - `Ctrl+Shift+E`: 快速加密选中文本
  - `Ctrl+Shift+D`: 快速解密选中文本
- **右键菜单**: 支持选中文本快速加解密（通过快捷键）

## 安装说明

### 1. 准备依赖库
在使用前，需要下载完整的依赖库文件：

1. **crypto-js**: 从 [cdnjs](https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js) 下载
2. **jsencrypt**: 从 [cdnjs](https://cdnjs.cloudflare.com/ajax/libs/jsencrypt/3.3.2/jsencrypt.min.js) 下载

将下载的文件替换 `libs/` 目录中的对应文件。

### 2. 安装扩展
1. 打开 Chrome 浏览器
2. 进入扩展管理页面 (`chrome://extensions/`)
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择本项目的根目录

### 3. 验证安装
- 在浏览器工具栏中应该能看到扩展图标
- 点击图标应该能打开加解密界面

## 使用方法

### 基本操作

#### 加密文本
1. 点击扩展图标打开界面
2. 在"加密"标签页中选择加密算法（AES/RSA/DES）
3. 输入要加密的文本
4. 点击"🔒 加密"按钮
5. 加密结果会显示在下方文本框中
6. 点击"📋 复制结果"可复制到剪贴板

#### 解密文本
1. 切换到"解密"标签页
2. 选择对应的解密算法（必须与加密时使用的算法一致）
3. 输入要解密的密文
4. 点击"🔓 解密"按钮
5. 解密结果会显示在下方文本框中

### 快捷键操作
- 在任意网页中选中文本
- 使用 `Ctrl+Shift+E` 快速加密
- 使用 `Ctrl+Shift+D` 快速解密
- 操作结果会通过通知显示

### 历史记录
- 所有操作会自动保存到历史记录
- 点击历史记录项可快速填充到输入框
- 支持清空历史记录

## 文件结构

```
mt-coder/
├── manifest.json          # 扩展配置文件
├── popup.html             # 弹窗界面HTML
├── popup.css              # 弹窗界面样式
├── popup.js               # 弹窗界面脚本
├── background.js          # 背景脚本
├── content.js             # 内容脚本
├── libs/                  # 依赖库目录
│   ├── aesUtil.js         # 适配的加解密工具
│   ├── crypto-js.min.js   # CryptoJS库（需下载）
│   └── jsencrypt.min.js   # JSEncrypt库（需下载）
├── icons/                 # 图标文件
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
└── README.md              # 说明文档
```

## 技术实现

### Manifest V3
- 使用最新的 Chrome Extension Manifest V3 规范
- Service Worker 作为背景脚本
- 声明式权限管理

### 核心模块
- **aesUtil.js**: 基于原始文件适配的加解密核心模块
- **popup.js**: 用户界面交互逻辑
- **background.js**: 后台服务和消息处理
- **content.js**: 页面内容脚本，支持快捷键和通知

### 数据存储
- 使用 Chrome Storage API 保存用户设置和历史记录
- 自动清理过期历史记录（超过一周）
- 支持最多20条历史记录

## 安全说明

### 加密算法
- **AES**: 使用CBC模式，PKCS7填充
- **RSA**: 支持公钥加密/私钥解密和私钥加密/公钥解密
- **密钥管理**: 使用预配置的密钥和IV

### 隐私保护
- 所有加解密操作在本地进行
- 不会向外部服务器发送任何数据
- 历史记录仅保存在本地浏览器中

## 开发说明

### 调试模式
在浏览器控制台中设置调试模式：
```javascript
localStorage.setItem('aes-tool-debug', 'true');
```

### 自定义配置
可以修改 `libs/aesUtil.js` 中的 `appConfig` 对象来使用自定义的密钥配置。

## 常见问题

### Q: 加密/解密失败怎么办？
A: 
1. 检查是否正确下载了依赖库文件
2. 确认输入的文本格式正确
3. 查看浏览器控制台是否有错误信息

### Q: 快捷键不工作？
A: 
1. 确认扩展已正确安装并启用
2. 检查是否与其他扩展的快捷键冲突
3. 尝试刷新页面后重新操作

### Q: 历史记录丢失？
A: 历史记录保存在浏览器本地存储中，清除浏览器数据会导致丢失。

## 更新日志

### v1.0.0 (2024-08-14)
- 初始版本发布
- 实现基本的AES加解密功能
- 添加用户友好的界面
- 支持操作历史记录
- 实现快捷键操作

## 许可证

本项目基于现有的 aesUtil.js 文件开发，请遵循相应的许可证要求。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个扩展。

## 联系方式

如有问题或建议，请通过 GitHub Issues 联系。
