# AES加解密工具 Chrome扩展 - 安装脚本
# 用于下载必要的依赖库文件

Write-Host "=== AES加解密工具 Chrome扩展安装脚本 ===" -ForegroundColor Green
Write-Host ""

# 检查libs目录是否存在
if (-not (Test-Path "libs")) {
    Write-Host "创建libs目录..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Path "libs" -Force
}

# 下载crypto-js库
Write-Host "正在下载 crypto-js 库..." -ForegroundColor Yellow
try {
    $cryptoJsUrl = "https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"
    Invoke-WebRequest -Uri $cryptoJsUrl -OutFile "libs/crypto-js.min.js"
    Write-Host "✓ crypto-js 下载完成" -ForegroundColor Green
} catch {
    Write-Host "✗ crypto-js 下载失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请手动下载: $cryptoJsUrl" -ForegroundColor Yellow
}

# 下载jsencrypt库
Write-Host "正在下载 jsencrypt 库..." -ForegroundColor Yellow
try {
    $jsencryptUrl = "https://cdnjs.cloudflare.com/ajax/libs/jsencrypt/3.3.2/jsencrypt.min.js"
    Invoke-WebRequest -Uri $jsencryptUrl -OutFile "libs/jsencrypt.min.js"
    Write-Host "✓ jsencrypt 下载完成" -ForegroundColor Green
} catch {
    Write-Host "✗ jsencrypt 下载失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请手动下载: $jsencryptUrl" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=== 安装完成 ===" -ForegroundColor Green
Write-Host ""
Write-Host "下一步操作:" -ForegroundColor Cyan
Write-Host "1. 打开 Chrome 浏览器" -ForegroundColor White
Write-Host "2. 进入扩展管理页面 (chrome://extensions/)" -ForegroundColor White
Write-Host "3. 开启'开发者模式'" -ForegroundColor White
Write-Host "4. 点击'加载已解压的扩展程序'" -ForegroundColor White
Write-Host "5. 选择当前目录: $(Get-Location)" -ForegroundColor White
Write-Host ""
Write-Host "如果下载失败，请手动下载依赖库文件到 libs/ 目录" -ForegroundColor Yellow
Write-Host ""

# 检查文件是否存在
Write-Host "检查文件状态:" -ForegroundColor Cyan
$files = @(
    "manifest.json",
    "popup.html", 
    "popup.css",
    "popup.js",
    "background.js",
    "content.js",
    "libs/aesUtil.js",
    "libs/crypto-js.min.js",
    "libs/jsencrypt.min.js"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        $size = (Get-Item $file).Length
        if ($size -gt 100) {
            Write-Host "✓ $file ($size bytes)" -ForegroundColor Green
        } else {
            Write-Host "⚠ $file ($size bytes) - 文件可能不完整" -ForegroundColor Yellow
        }
    } else {
        Write-Host "✗ $file - 文件不存在" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
