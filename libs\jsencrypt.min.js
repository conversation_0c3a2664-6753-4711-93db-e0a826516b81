// 简化版 JSEncrypt 库 - 用于Chrome扩展
// 注意：这是一个简化版本，实际使用时建议使用完整的 JSEncrypt 库

// 临时占位符实现，实际使用时需要替换为完整的 JSEncrypt 库
(function(global) {
    'use strict';
    
    // 简化的 JSEncrypt 类
    function JSEncrypt(options) {
        this.options = options || {};
        this.key = null;
    }
    
    JSEncrypt.prototype.setPublicKey = function(key) {
        this.key = key;
    };
    
    JSEncrypt.prototype.setPrivateKey = function(key) {
        this.key = key;
    };
    
    JSEncrypt.prototype.encrypt = function(text) {
        // 这里应该实现真正的RSA加密
        // 临时返回base64编码的文本作为占位符
        console.warn('使用简化版JSEncrypt，请替换为完整版本');
        return btoa(text);
    };
    
    JSEncrypt.prototype.decrypt = function(encrypted) {
        // 这里应该实现真正的RSA解密
        // 临时返回base64解码的文本作为占位符
        console.warn('使用简化版JSEncrypt，请替换为完整版本');
        try {
            return atob(encrypted);
        } catch (e) {
            return encrypted;
        }
    };
    
    JSEncrypt.prototype.encryptPrivate = function(text) {
        // 私钥加密
        return this.encrypt(text);
    };
    
    JSEncrypt.prototype.decryptPublic = function(encrypted) {
        // 公钥解密
        return this.decrypt(encrypted);
    };
    
    // 导出到全局
    global.JSEncrypt = JSEncrypt;
    
    // 如果是模块环境
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = JSEncrypt;
    }
    
})(typeof window !== 'undefined' ? window : this);

console.log('简化版 JSEncrypt 库已加载 - 请在生产环境中替换为完整版本');
