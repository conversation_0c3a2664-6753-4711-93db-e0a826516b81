// 适配Chrome扩展的AES加解密工具
// 基于原始 aesUtil.js 文件修改

const appConfig = {
  key: "Qb4kkGH1xewlaRC7IzsmFJTLwcs3RRS1h5Qb6Tt9WEjEtDGobkSvxULlWUPbt3JKn4xoWFUD1rL97//iA8HD9gDNr6xiXf0iw8/k1i9fXw1eyXWGTdeOYmCTbnIb+gsNKKRWogi811SSXUF3/qpKfJzzDB9Q9pFb4Xsxv/1e3mYX2HFIu5/MVlNQ7CN+Kai0Y80reUxOskEZlGHcnvZXCNC5WngNXecihyBBGNI58dXuPWHgR8SHAvyjCW4JeAD6D3FsHjewy96RpSNbuqvYoS7wAsvHllPYINwxQLg+2N2jILc4J0PaT5myO2KlJq3By/w8Mk0FiVLMcClcWV9xYg==",
  iv: "jKTey2lC2X2/yhX8tB2TlpGdXJOQGxT7zwBzIwmi4RRYxTOz+hm00wPzTiqkHLgBqC1aXSy5b/+An3D5lSPq8Y6j5I9JIM+SMA/oTKaCcZh2Mb5tXNHpaK3EMcn+kjS909hhRe95J8yYf4/jtJt2AeTm1SrfCf5Ejv067Mw5giwlD84HJ6lu9XR005UFTNPLhub46nwYjFGc0z9/qr6M6K8EvX07cZsL1OuknTjlLrXHXE8/8pdcPd/IlG5bmU3LeItBQYnyNZJKaP13gLkbXgDZvzr0V9df49CG5WPycIdHePyun/ib2u9qpXKu4fZgvHdhiIoL426EePM6gfewvw==",
  PublicKey: "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkNbjFqvkjzL3YCV59n2Uwg8LLVgOeD+DeA8/5/oatsYidJUPAAFM5cnaxCHC4Ygl9czB2lSHlAfA16o0TlbAtRLHXfXyxu6pfoUpF6nSZzGgkg+66ltZbYSapaELYb9Bnb1ALk3uX+/seAl1GeFNYozLavCZHBlQJzYo2usJsEuZSaB/8nTU8ZTP0VNnTGImRcw2de1JEwgCKdteq5gFezDnu46P26o96GEogFTwlsDjefMnxp6Sk7tlnM8IIbKayTvQ2467zu0/ggifVnSC5LE69o5yv7hdHvEoUFzk8XN+pasdaLIIwmbshCxwIWcKbrsRPkvoTDZChc5JJR+DZwIDAQAB",
};

let _key;
let _iv;

function encryptAES(param) {
  let { key, iv, PublicKey: publicKey } = appConfig;
  key = getAesKey(key, publicKey);
  iv = getAesIv(iv, publicKey);
  key = CryptoJS.enc.Utf8.parse(key);
  iv = CryptoJS.enc.Utf8.parse(iv);
  let srcs = CryptoJS.enc.Utf8.parse(param);
  let encrypted = CryptoJS.AES.encrypt(srcs, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  return encrypted.toString();
}

function getAesKey(key, publicKey) {
  if (!_key) {
    _key = decryptRSA(key, publicKey);
  }
  return _key;
}

function getAesIv(iv, publicKey) {
  if (!_iv) {
    _iv = decryptRSA(iv, publicKey);
  }
  return _iv;
}

function encryptRSA(str, publicKey, keyGenSize) {
  const encrypt = new JSEncrypt({
    default_key_size: keyGenSize,
  });
  encrypt.setPrivateKey(getPrivateKey(publicKey));
  return encrypt.encryptPrivate(str);
}

function decryptRSA(encrypted, publicKey, keyGenSize = 2048) {
  const decrypt = new JSEncrypt({
    default_key_size: keyGenSize,
  });
  decrypt.setPublicKey(getPubKey(publicKey));
  return decrypt.decryptPublic(encrypted);
}

function encryptRSAByPublic(str, publicKey, keyGenSize = 2048) {
  const encrypt = new JSEncrypt({
    default_key_size: keyGenSize,
  });
  encrypt.setPublicKey(getPubKey(publicKey));
  return encrypt.encrypt(str);
}

function decryptRSAByPrivate(encrypted, privateKey, keyGenSize = 2048) {
  const decrypt = new JSEncrypt({
    default_key_size: keyGenSize,
  });
  decrypt.setPrivateKey(getPrivateKey(privateKey));
  return decrypt.decrypt(encrypted);
}

function getPubKey(key) {
  return `-----BEGIN PUBLIC KEY-----${key}-----END PUBLIC KEY-----`;
}

function getPrivateKey(key) {
  return `-----BEGIN PRIVATE KEY-----${key}-----END PRIVATE KEY-----`;
}

function decryptAES(result) {
  let { key, iv, PublicKey: publicKey } = appConfig;
  key = getAesKey(key, publicKey);
  iv = getAesIv(iv, publicKey);
  key = CryptoJS.enc.Utf8.parse(key);
  iv = CryptoJS.enc.Utf8.parse(iv);
  let base64string = CryptoJS.enc.Base64.parse(result.replace(/[\r\n]/g, ""));
  let base64Word = base64string.toString();
  let encryptedHexStr = CryptoJS.enc.Hex.parse(base64Word);
  let srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr);
  let decrypt = CryptoJS.AES.decrypt(srcs, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  let decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
  return decryptedStr.toString();
}

function encryptDES(str) {
  let { key, iv, PublicKey: publicKey } = appConfig;
  key = getAesKey(key, publicKey);
  iv = getAesIv(iv, publicKey);
  let srcs = CryptoJS.enc.Utf8.parse(str);
  let encrypted = CryptoJS.DES.encrypt(srcs, key, {
    iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  return encrypted.toString();
}

function decryptDES(result) {
  let { key, iv, PublicKey: publicKey } = appConfig;
  key = getAesKey(key, publicKey);
  iv = getAesIv(iv, publicKey);
  let base64string = CryptoJS.enc.Base64.parse(result.replace(/[\r\n]/g, ""));
  let base64Word = base64string.toString();
  let encryptedHexStr = CryptoJS.enc.Hex.parse(base64Word);
  let srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr);
  let decrypt = CryptoJS.DES.decrypt(srcs, key, {
    iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  let decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
  return decryptedStr.toString();
}

// 导出函数供Chrome扩展使用
window.AESUtil = {
  encryptAES,
  decryptAES,
  encryptRSA,
  decryptRSA,
  encryptRSAByPublic,
  decryptRSAByPrivate,
  encryptDES,
  decryptDES,
  appConfig, // 导出配置供外部访问
};
